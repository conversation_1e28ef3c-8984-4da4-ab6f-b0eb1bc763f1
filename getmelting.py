# import mysql.connector
import mariadb
import pandas as pd
from typing import Any
from mysql.connector import Error

DB_CONFIG = {
    'host': '***********',
    'port': 3304,
    'user': 'qcv2user',
    'password': 'qcv2P@ss',
    'database': 'qcvent02',
}

def get_melting_data(id_param: int) -> pd.DataFrame:
    QUERY = f"""SELECT * FROM meltings WHERE id > {int(id_param)}"""
    try:
        conn = mariadb.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(QUERY)
        result_df = pd.DataFrame(cursor.fetchall(), columns=[i[0] for i in cursor.description])
        return result_df
    except Error as e:
        print(f"Error: {e}")
        return pd.DataFrame()

# Example usage
# if __name__ == "__main__":
#     result_df = get_melting_data(0)
#     print(result_df.head())