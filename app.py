from getmelting import get_melting_data
from setmelting import get_max_id

if __name__ == "__main__":
    row=get_max_id()
    if row['max_id'].values[0] == None:
        print("No data found in target table")
        max_id = 0
    else:
        max_id = row['max_id'].values[0]
        print(f"Max id found: {max_id}")
    
    # print("executing query")
    result_df = get_melting_data(max_id)
    print(f"DataFrame shape: {result_df.shape}")  # Debug: Check if data was fetched
    print(result_df.head())