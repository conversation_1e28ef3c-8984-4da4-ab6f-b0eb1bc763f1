# import mysql.connector
import mariadb
import pandas as pd
from typing import Any
from mysql.connector import Error

DB_CONFIG = {
    'host': '***********',
    'port': 8308,
    'user': 'qcv3user',
    'password': 'qcv3P@ss',
    'database': 'qcvent03',
}

def get_max_id()-> pd.DataFrame:
    QUERY = f"""SELECT MAX(id) as max_id FROM meltings"""
    try:
        conn = mariadb.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(QUERY)
        # result = cursor.fetchall()
        # return result
        result_df = pd.DataFrame(cursor.fetchall(), columns=[i[0] for i in cursor.description])
        return result_df
    except Error as e:
        print(f"Error: {e}")
        return pd.DataFrame()

def fill_melting_table(df: pd.DataFrame) -> None:
    try:
        conn = mariadb.connect(**DB_CONFIG)
        cursor = conn.cursor()
        for _, row in df.iterrows():
            QUERY = f"""INSERT INTO meltings (id, barcode, metal, color, alloy_weight, original_weight"""

# Example usage
# if __name__ == "__main__":
#     result_df = get_melting_data(0)
#     print(result_df.head())